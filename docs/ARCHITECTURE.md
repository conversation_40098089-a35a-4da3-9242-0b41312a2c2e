                  ┌─────────────────┐
                  │     UI Layer    │
                  │   (Components)  │
                  └────────┬────────┘
                           │
                           ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   State Layer   │  │  Business Logic │  │     Routing     │
│ (<PERSON><PERSON>, Context)│◄─┤     (Hooks)     │──┤  (Navigation)   │
└────────┬────────┘  └────────┬────────┘  └─────────────────┘
         │                    │
         └──────────┬─────────┘
                    │
                    ▼
           ┌─────────────────┐
           │    Data Layer   │
           │ (React Query,   │
           │  Firebase SDK)  │
           └────────┬────────┘
                    │
                    ▼
           ┌─────────────────┐
           │  External APIs  │
           │    (Firebase)   │
           └─────────────────┘

/** Example of component composition **/

// src/pages/Challenges/ViewChallengeScreen.tsx
const ViewChallengeContent: React.FC<{challenge: Challenge}> = memoComponent(({challenge}) => (
  <>
    <ChallengeInitializeStage challenge={challenge} />

    {challenge.flags?.isDraft && (
      <StickyAboveNavigation>
        <Box flexDirection='row' justifyContent='flex-end'>
          <Chip icon='file-document-edit-outline' mode='outlined' textStyle={{color: 'red'}}>
            DRAFT STATE
          </Chip>
        </Box>
      </StickyAboveNavigation>
    )}

    <EditGoalModalChallenge />

    {DEV_FEATURE_FLAGS().isDebugViewEnabled && (
      <CopyClipboardWrapper text={challenge.id}>
        <Text>{challenge.id}</Text>
      </CopyClipboardWrapper>
    )}

    <ChallengeHeaderPhoto challenge={challenge} />

    <ChallengeAcceptReject hasHeading hasPb challenge={challenge} />

    <ChallengeProgress
      challenge={challenge}
      header={CONTENT_CODES().CHALLENGE.VIEW.PROGRESS_HEADER}
    />

    <ChallengeCountdown challenge={challenge} />

    <ChallengeLeaderboard challenge={challenge} />
  </>
));

// src/contexts/authContext.ts
export const useIsAuthenticated = () => {
  const hasFirebaseUser = !!useFirebaseUser();
  const hasAppUser = !!useAppUser();
  return hasFirebaseUser && hasAppUser;
};

/** Firestore query example **/
// src/contexts/firestore/appUserContext.ts
export const useAppUserByEmail = (email: string) => {
  const {data, error, isLoading} = useFirestoreQuery({
    q: () => query(db.appUsers, where('email', '==', email)),
    queryKey: ['useAppUserByEmail', email],
    isEnabled: !!email,
    collectionReference: db.appUsers,
  });
  const appUser = data?.[0];
  return {appUser, isLoading, error};
};

/** Fetching the daily wellness quiz **/
// src/contexts/firestore/wellnessQuizContext.ts
export const useWellnessQuiz = () => {
  const isoDate = useIsoDateEveryDayChanged();

  return useQuery({
    queryKey: ['useWellnessQuiz', isoDate],
    queryFn: async () => {
      const response = await firebaseApi.getWellnessQuiz(isoDate);
      if (response.status !== StatusCodes.OK_200) {
        throw new Error('Failed to fetch wellness quiz');
      }
      return response.data;
    },
    staleTime: MS_15_MINUTES,
  });
};

/** Example of Jotai atoms usage **/
// src/contexts/snacks/snackContext.ts
type SnackType = {
  displayText: string;
  duration: number;
  id: string;
};

type SnackContextType = {
  snacks: SnackType[];
};

// NOTE: this is NOT imported directly, only functions wrapping reading/writing to the atom are exported
const snackContextAtom = atom<SnackContextType>({snacks: []});

export const useSnackContext = () => useAtomValue(snackContextAtom);

export const useAddSnack = () => {
  // ...
};

export const useRemoveSnack = () => {
  // ...
};

firestore/
├── users/
│   └── {userId}/
│       ├── profile
│       ├── settings
│       └── stats
├── challenges/
│   └── {challengeId}/
│       ├── details
│       ├── participants/
│       │   └── {userId}
│       └── teams/
│           └── {teamId}/
│               └── members/
│                   └── {userId}
└── organizations/
    └── {orgId}/
        ├── details
        └── members/
            └── {userId}

// Navigation structure example
<Stack.Navigator>
  <Stack.Screen name="Home" component={HomeScreen} />
  <Stack.Screen name="Profile" component={ProfileScreen} />
  <Stack.Screen name="Challenge" component={ChallengeScreen} />
</Stack.Navigator>