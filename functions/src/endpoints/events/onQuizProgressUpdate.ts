import {
  handleAppUserQuizStreakMetadata,
  handleAppUserQuizStreakRecalculateAll,
} from '../../endpointHelpers';
import {FIRESTORE_PATHS, LOGGER, onDocumentWritten} from '../../firebase';
import {getAppUserById} from '../../firestore';
import {type QuizStreakDocument} from '../../types';
import {isDeepEqual, withLogging} from '../../utils';

export const onQuizProgressUpdate = onDocumentWritten(
  {
    memory: '512MiB',
    document: FIRESTORE_PATHS.quizProgress,
  },
  withLogging(async event => {
    const snapshot = event.data;
    if (!snapshot) {
      LOGGER.warn('No data associated with the event');
      return;
    }
    const quizProgress = snapshot.after.data() as QuizStreakDocument | undefined;
    const previousQuizProgress = snapshot.before.data() as QuizStreakDocument | undefined;
    const isDelete = !quizProgress && !!previousQuizProgress;
    const isCreate = !!quizProgress && !previousQuizProgress;
    const isUpdate = !!quizProgress && !!previousQuizProgress;

    if (isDelete) {
      LOGGER.debug(`Deleting quizProgress document ${previousQuizProgress.id}`);

      // Recalculate all streak data if document is deleted
      const appUser = await getAppUserById(previousQuizProgress.userId);
      if (!appUser) return;
      await handleAppUserQuizStreakRecalculateAll(appUser);
    } else if (isCreate || isUpdate) {
      LOGGER.debug(`Creating or updating quizProgress document ${quizProgress.id}`);
      const hasChanged = !isDeepEqual(quizProgress, previousQuizProgress);
      if (!hasChanged) {
        LOGGER.debug(`No change detected for quizProgress document ${quizProgress.id}`);
        return;
      }

      await handleAppUserQuizStreakMetadata(quizProgress);
    }
  }, 'onQuizProgressUpdate'),
);
