import {DEFAULT_TIME_ZONE} from '../../constants';
import {
  handleAppUserHealthDataMetadata,
  handleAppUserHealthDataStatsChange,
} from '../../endpointHelpers';
import {FIRESTORE_PATHS, LOGGER, onDocumentWritten} from '../../firebase';
import {
  getIsoMonthStringFromDate,
  type HealthData,
  type IsoMonth,
  isValidIsoMonth,
  type UUIDString,
} from '../../types';
import {isUuid, withLogging} from '../../utils';

export const onHealthDataDocumentUpdate = onDocumentWritten(
  {
    memory: '1GiB',
    document: FIRESTORE_PATHS.appUserHealthData,
  },
  withLogging(async event => {
    const snapshot = event.data;
    const {appUserId, isoMonth} = event.params as {
      appUserId: UUIDString | undefined;
      isoMonth: IsoMonth | undefined;
    };
    if (!snapshot || !isUuid(appUserId) || !isValidIsoMonth(isoMonth)) {
      LOGGER.warn('No data or bad data associated with the event', event.params);
      return;
    }

    // const prevHealthData = snapshot.before.data() as HealthData | undefined;
    const healthData = snapshot.after.data() as HealthData | undefined;
    if (!healthData) return;

    const currentDate = new Date();
    const currentIsoMonth = getIsoMonthStringFromDate(
      currentDate,
      DEFAULT_TIME_ZONE,
    );
    if (isoMonth !== currentIsoMonth) {
      LOGGER.debug(
        `Skipping health data update for month ${isoMonth} since not current month ${currentIsoMonth}`,
      );
      return;
    }

    await handleAppUserHealthDataMetadata(appUserId);
    await handleAppUserHealthDataStatsChange(appUserId, healthData);
  }, 'onHealthDataDocumentUpdate'),
);
