import {randomUUID} from 'node:crypto';
import {formatInTimeZone} from 'date-fns-tz';
import type {Response} from 'express';
import type {Transaction} from 'firebase-admin/firestore';
import type {
  AdminUpdateUserResponseDTO,
  AppUser,
  ChallengeJoinInviteCode,
  ImportUserDTO,
  SignUpAppUserRequestDTO,
  UUIDString,
} from '../types';
import {DEFAULT_TIME_ZONE} from '../constants';
import {auth, LOGGER, projectId} from '../firebase';
import {
  getAppUserByEmail,
  getAppUserByPhoneNumber,
  getChallengeById,
  getChallengeGroupDocumentById,
  getInviteCodeById,
  getOrganizationById,
  updateAppUserByIdTransaction,
} from '../firestore';
import {getInviteCodePayload, InviteCodeActionType, isGroupsChallenge, isIndividualChallenge, isTeamsChallenge, patternMatch, StatusCodes, UserType} from '../types';
import {
  appendToGoogleSheet,
  arrayPartition,
  arraysToString,
  emptyUser,
  getAllFulfilledOrThrow,
  isDefinedString,
  isValidEmailTest,
  isValidPhoneTest,
  runTransaction,
} from '../utils';
import {handleChallengeJoinPayload} from './challengeImportHelpers';
import {sendWelcomeEmailByAppUser, sendWelcomeEmailByUserIds} from './emailHelpers';

const createNewUser = async (appUser: AppUser, transaction: Transaction) => {
  LOGGER.debug(`Creating new user with id=${appUser.id} and email=${appUser.email}...`);
  // Creates the Firebase Auth user
  const _userRecord = await auth.createUser({
    email: appUser.email,
    // eslint-disable-next-line @typescript-eslint/naming-convention -- library API
    emailVerified: false,
    password: randomUUID(),
    // eslint-disable-next-line @typescript-eslint/naming-convention -- library API
    disabled: false,
  });
  // Creates the appUser document
  updateAppUserByIdTransaction(appUser.id, appUser, transaction);
};

export const getAuthUserByEmail = async (email: string) => {
  try {
    return await auth.getUserByEmail(email);
  } catch {
    return;
  }
};

export const markEmailAsVerified = async (email: string) => {
  try {
    const userRecord = await getAuthUserByEmail(email);
    if (!userRecord) return;
    // eslint-disable-next-line @typescript-eslint/naming-convention -- library API
    await auth.updateUser(userRecord.uid, {emailVerified: true});
    LOGGER.debug(`Successfully marked email as verified for ${email}`);
  } catch (error) {
    LOGGER.error(`Error making email ${email} as verified`, error);
  }
};

const doesAppuserExistByEmail = async (email: string) => {
  const appUser = await getAppUserByEmail(email);
  return !!appUser;
};

const doesAppUserExistByPhoneNumber = async (phoneNumber: string) => {
  const appUser = await getAppUserByPhoneNumber(phoneNumber);
  return !!appUser;
};

const isAuthUserExistByEmail = async (email: string) => !!(await getAuthUserByEmail(email));

export const getAuthUserByPhoneNumber = async (phoneNumber: string) => {
  try {
    return await auth.getUserByPhoneNumber(phoneNumber);
  } catch {
    return;
  }
};

const isAuthUserExistByPhoneNumber = async (phoneNumber: string) => {
  try {
    return !!(await getAuthUserByPhoneNumber(phoneNumber));
  } catch {
    return false;
  }
};

export const doesUserExistEmail = async (email: string) => {
  const isAppUserExists = await doesAppuserExistByEmail(email);
  const isAuthUserExists = await isAuthUserExistByEmail(email);

  return isAppUserExists || isAuthUserExists;
};

export const doesUserExistPhoneNumber = async (phoneNumber: string) => {
  const isAppUserExists = await doesAppUserExistByPhoneNumber(phoneNumber);
  const isAuthUserExists = await isAuthUserExistByPhoneNumber(phoneNumber);

  return isAppUserExists || isAuthUserExists;
};

const cleanAndCheckUserOnlyEmail = async (user: Partial<ImportUserDTO>) => {
  const {email} = user;
  const cleanEmail = isDefinedString(email) ? email.trim().toLowerCase() : undefined;
  if (!isDefinedString(cleanEmail)) {
    LOGGER.debug('Missing email', user);
    return `❌ User with value ${JSON.stringify(user)} is missing email`;
  }
  if (!isValidEmailTest(cleanEmail)) {
    LOGGER.debug('Invalid email', user);
    return `❌ User with email ${cleanEmail} is invalid`;
  }

  return {email: cleanEmail};
};

const cleanAndCheckUser =
  async (user: Partial<ImportUserDTO>) => {
    const {email, firstName, lastName, organizationId, phoneNumber} = user;
    const cleanFirstName = isDefinedString(firstName) ? firstName.trim() : undefined;
    const cleanLastName = isDefinedString(lastName) ? lastName.trim() : undefined;
    const cleanEmail = isDefinedString(email) ? email.trim().toLowerCase() : undefined;
    const cleanPhoneNumber = isDefinedString(phoneNumber)
      ? phoneNumber.replaceAll(/\D/g, '')
      : undefined;
    if (!isDefinedString(cleanEmail)) {
      LOGGER.debug('Missing email', user);
      return `❌ User with value ${JSON.stringify(user)} is missing email`;
    }
    if (!isDefinedString(cleanFirstName)) {
      LOGGER.debug('Missing firstName', user);
      return `❌ User with email ${cleanEmail} is missing firstName`;
    }
    if (!isDefinedString(cleanLastName)) {
      LOGGER.debug('Missing lastName', user);
      return `❌ User with email ${cleanEmail} is missing lastName`;
    }
    if (!isValidEmailTest(cleanEmail)) {
      LOGGER.debug('Invalid email', user);
      return `❌ User with email ${cleanEmail} is invalid`;
    }
    if (isDefinedString(organizationId)) {
      const doesOrganizationExist = await getOrganizationById(organizationId);
      if (!doesOrganizationExist) {
        LOGGER.debug('Invalid organizationId', user);
        return `❌ User with email ${cleanEmail} has invalid organizationId of '${organizationId}'`;
      }
    }
    if (isDefinedString(cleanPhoneNumber) && !isValidPhoneTest(cleanPhoneNumber)) {
      LOGGER.debug('Invalid cleaned phone number', user);
      return `❌ User with email ${cleanEmail} has invalid phone number of '${cleanPhoneNumber}'`;
    }
    const doesExist = await doesUserExistEmail(cleanEmail);
    if (doesExist) {
      LOGGER.debug('User already exists', user);
      return `❌ User with email ${cleanEmail} already exists`;
    }

    return emptyUser({
      firstName: cleanFirstName,
      lastName: cleanLastName,
      email: cleanEmail,
      type: UserType.CLIENT,
      ...(cleanPhoneNumber && {phoneNumber: cleanPhoneNumber}),
      ...(organizationId && {organizationIds: [organizationId]}),
    });
  };

const deduplicateUsers = <T extends {email: string}>(cleanUsers: (string | T)[]) => {
  const seenEmails = new Set<string>();

  return cleanUsers.map(item => {
    // Ignore error messages
    if (isDefinedString(item)) return item;

    // Check if have seen this email
    if (seenEmails.has(item.email)) {
      // Duplicate AppUser -> Convert to error message
      return `❌ That email ${item.email} was used previously in this import`;
    }

    // Unique AppUser -> Add email to seenEmails
    seenEmails.add(item.email);
    return item;
  });
};

export const cleanAndCheckUsersOnlyEmail = async (users: ImportUserDTO[]) => {
  const cleanedUsersPromises = users.map(cleanAndCheckUserOnlyEmail);
  const cleanedUsersResults = await Promise.allSettled(cleanedUsersPromises);
  const cleanUsers: (string | {email: string})[] = cleanedUsersResults.map(result => {
    if (result.status === 'rejected') {
      return result.reason as string;
    }
    return result.value;
  });
  const cleanUsersDeduped = deduplicateUsers(cleanUsers);
  return arrayPartition(cleanUsersDeduped, isDefinedString);
};

export const sendAppUsersWelcomeEmail = async (cleanedEmails: {email: string}[]) => {
  const appUserPromises = cleanedEmails.map(u => getAppUserByEmail(u.email));
  const appUsers = await getAllFulfilledOrThrow(appUserPromises);
  const userIds = appUsers.map(u => u.id);
  const welcomeEmailResults = await sendWelcomeEmailByUserIds(userIds);

  return welcomeEmailResults.reduce(
    (acc, result) => {
      if (!result.isSuccess) {
        return {
          ...acc,
          errorMessages: [...acc.errorMessages, result.message],
        };
      }
      return {
        ...acc,
        successMessages: [...acc.successMessages, result.message],
      };
    },
    {successMessages: [] as string[], errorMessages: [] as string[]},
  );
};

export const cleanAndCheckUsers = async (
  users: ImportUserDTO[],
): Promise<[string[], AppUser[]]> => {
  const cleanedUsersPromises = users.map(cleanAndCheckUser);
  const cleanedUsersResults = await Promise.allSettled(cleanedUsersPromises);
  const cleanUsers = cleanedUsersResults.map(result => {
    if (result.status === 'rejected') {
      return result.reason as string;
    }
    return result.value;
  });
  const cleanUsersDeduped = deduplicateUsers(cleanUsers);
  return arrayPartition(cleanUsersDeduped, isDefinedString);
};

const createAppUserAndSendEmail =
  (options: {isCreate: boolean; isSendEmail: boolean}) => async (user: AppUser) => {
    try {
      if (options.isCreate) {
        await runTransaction(async transactionInternal => {
          await createNewUser(user, transactionInternal);
        }, 'create new user');
      }
      if (options.isSendEmail) {
        await sendWelcomeEmailByAppUser(user);
      }
    } catch {
      return {
        id: user.id,
        isError: true,
        message: `❌ User with email ${user.email} was not created successfully`,
      } as const;
    }
    const createStr = options.isCreate ? 'created' : '*not* created';
    const emailStr = options.isSendEmail ? 'sent email' : '*not sent email*';
    const message = `✅ User with email ${user.email} ${createStr} and ${emailStr} successfully`;

    return {
      id: user.id,
      message,
    };
  };

export const createAppUsersAndSendEmails = async (
  users: AppUser[],
  options: {isCreate: boolean; isSendEmail: boolean},
) => {
  const importUserPromises = users.map(createAppUserAndSendEmail(options));
  const importUserResults = await Promise.allSettled(importUserPromises);

  // Save the error messages in the response, and save which user ids were created
  return importUserResults.reduce(
    (acc, result) => {
      if (result.status !== 'fulfilled') {
        LOGGER.warn('Error importing user:', result.reason);
        return {
          ...acc,
          errorMessages: [...acc.errorMessages, result.reason as string],
        };
      }
      if (result.value.isError) {
        return {
          ...acc,
          errorMessages: [...acc.errorMessages, result.value.message],
        };
      }
      return {
        ...acc,
        successMessages: [...acc.successMessages, result.value.message],
      };
    },
    {successMessages: [] as string[], errorMessages: [] as string[]},
  );
};

const SIGN_UP_GOOGLE_SHEET_ID = '1KyjSJdn-QUdJip41rltvxluIrGj4bEqU8hzZ0UbXQcg';

const getChallengeInfoMetadata = async (
  inviteCodeMetadata: ChallengeJoinInviteCode | undefined,
): Promise<{challengeName?: string | undefined; teamName?: string | undefined}> => {
  if (!inviteCodeMetadata) {
    return {};
  }
  const inviteCodeDoc = await getInviteCodeById(inviteCodeMetadata.inviteCode);
  if (!inviteCodeDoc) {
    return {};
  }
  const challengePayload = getInviteCodePayload(
    inviteCodeDoc,
    InviteCodeActionType.JOIN_CHALLENGE,
  );
  if (!challengePayload?.challengeId) {
    return {};
  }
  const challenge = await getChallengeById(challengePayload.challengeId);
  if (!challenge) {
    return {};
  }
  const {groupId, teamId} = inviteCodeMetadata ?? {};
  const teamName = await patternMatch(challenge)
    .when(isIndividualChallenge, () => Promise.resolve(undefined))
    .when(isTeamsChallenge, c => Promise.resolve(c.teams?.find(t => t.id === teamId)?.name))
    .when(isGroupsChallenge, async c => {
      if (!groupId) {
        return;
      }
      const group = await getChallengeGroupDocumentById(c.id)(groupId);
      return group?.name;
    })
    .exhaustive();
  return {
    challengeName: challenge?.challengeName,
    teamName,
  };
};

const handleSignUpGoogleSheet = async (
  appUser: AppUser,
  inviteCodeMetadata: ChallengeJoinInviteCode | undefined,
) => {
  const challengeInfo = await getChallengeInfoMetadata(inviteCodeMetadata);

  // Format current date/time in PST timezone with "MM/DD/YYYY HH:MM:SS" format
  const currentDateTime = formatInTimeZone(
    new Date(),
    DEFAULT_TIME_ZONE,
    'MM/dd/yyyy HH:mm:ss a',
  );

  // Build the row of data
  // [A] = date time, in PST timezone, with format "MM/DD/YYYY HH:MM:SS"
  // [B] = email
  // [C] = firstName
  // [D] = lastName
  // [E] = phoneNumber
  // [F] = challenge name
  // [G] = group/team name
  const rowData: string[] = [
    currentDateTime,
    appUser.email,
    appUser.firstName,
    appUser.lastName,
    appUser.phoneNumber || '',
    challengeInfo.challengeName || '',
    challengeInfo.teamName || '',
  ];

  try {
    // Append the row of data to the sheet
    const sheetTitle = projectId === 'fly-fit' ? 'Sign Ups' : 'Sign Ups DEV';
    await appendToGoogleSheet([rowData], SIGN_UP_GOOGLE_SHEET_ID, sheetTitle);
    LOGGER.debug(`Successfully added user ${appUser.email} to Sign Up Google Sheet`);
  } catch (error) {
    LOGGER.error('Error adding user to Google Sheet:', error);
    throw error;
  }
};

export const handleSignUpAppUserRequest = async (
  {email, firstName, inviteCodeMetadata, lastName, phoneNumber}: Partial<SignUpAppUserRequestDTO>,
  response: Response,
) => {
  if (!isDefinedString(email)) {
    LOGGER.warn('Email is required');
    response.status(StatusCodes.BAD_REQUEST_400).send(false).end();
    return;
  }
  if (!isDefinedString(firstName) || !isDefinedString(lastName)) {
    LOGGER.warn('Missing firstName or lastName');
    response.status(StatusCodes.BAD_REQUEST_400).send(false).end();
    return;
  }
  if (phoneNumber && !isValidPhoneTest(phoneNumber)) {
    LOGGER.warn('Invalid phone number');
    response.status(StatusCodes.BAD_REQUEST_400).send(false).end();
    return;
  }

  const doesExist = await doesUserExistEmail(email);
  if (doesExist) {
    LOGGER.warn('User already exists');
    response.status(StatusCodes.BAD_REQUEST_400).send(false).end();
    return;
  }

  // Handle new user creation
  const appUser = emptyUser({
    firstName,
    lastName,
    email,
    type: UserType.CLIENT,
    ...(phoneNumber && {phoneNumber}),
  });
  try {
    await createAppUsersAndSendEmails([appUser], {isCreate: true, isSendEmail: true});

    if (inviteCodeMetadata) {
      await handleChallengeJoinPayload(inviteCodeMetadata, appUser);
    }

    // Add user to Google Sheet
    await handleSignUpGoogleSheet(appUser, inviteCodeMetadata);

    response.status(StatusCodes.OK_200).send(true).end();
  } catch (error) {
    LOGGER.error('Error creating new user:', error);
    response.status(StatusCodes.INTERNAL_SERVER_ERROR_500).send(false).end();
  }
};

// Helper function to update existing user
export const updateExistingUser = async (userData: {
  email: string;
  firstName: string;
  isManualEntryEnabled?: boolean | undefined;
  lastName: string;
  organizationIds?: UUIDString[];
  phoneNumber?: string;
  type: UserType;
}): Promise<AdminUpdateUserResponseDTO> => {
  let result: AdminUpdateUserResponseDTO = '❌ Failed to update user';

  await runTransaction(async transaction => {
    try {
      const appUser = await getAppUserByEmail(userData.email, transaction);
      if (!appUser) {
        throw new Error(`User with email ${userData.email} not found in transaction`);
      }

      updateAppUserByIdTransaction(appUser.id, userData, transaction);

      LOGGER.debug('Updated user successfully', {id: appUser.id, email: appUser.email});
      result = {
        message: '✅ User updated successfully',
        id: appUser.id,
        email: appUser.email,
      };
    } catch (error) {
      LOGGER.error('Error updating existing user:', error);
    }
  }, 'update user');

  return result;
};

// Helper function to create new user
export const createNewUserAndSendEmail = async (userData: {
  email: string;
  firstName: string;
  lastName: string;
  organizationIds?: UUIDString[];
  phoneNumber?: string;
  type: UserType;
}): Promise<AdminUpdateUserResponseDTO> => {
  const appUser = emptyUser(userData);

  const result = await createAppUsersAndSendEmails([appUser], {
    isCreate: true,
    isSendEmail: true,
  });

  return {
    message: arraysToString(result.successMessages, result.errorMessages),
    id: appUser.id,
    email: userData.email,
  };
};
