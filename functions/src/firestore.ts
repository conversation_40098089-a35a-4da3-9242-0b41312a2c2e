/* eslint-disable max-lines -- TODO: refactor */
import {
  type CollectionGroup,
  type CollectionReference,
  type DocumentReference,
  FieldPath,
  type Transaction,
} from 'firebase-admin/firestore';
import {
  DEFAULT_QUIZ_START_DATE_OFFSET,
  DEFAULT_QUIZ_START_ISO_DATE,
  DEFAULT_START_ISO_DATE,
  MS_1_MINUTE,
  MS_24_HOURS,
} from './constants';
import {auth, db, LOGGER} from './firebase';
import {
  type AppUser,
  type AppUserNotificationDocument,
  type AuthToken,
  type BaseAppUser,
  type Challenge,
  CHALLENGE_PARTICIPANTS_GROUPING_TYPES,
  type ChallengeGroupDocument,
  type ChallengeGroupDocumentMetadata,
  ChallengeGroupingType,
  type ChallengeGroupParticipantDocument,
  ChallengeInviteStatus,
  ChallengeParticipantRole,
  type DateInterval,
  dateToRawTimestamp,
  type DocumentIdTypeDoc,
  FirebaseAdminTimestamp,
  getNowAsRawTimestampServer,
  type GroupsChallenge,
  type HealthData,
  type InviteCodeDocument,
  isAppUserAdmin,
  isAppUserClient,
  isAppUserTrainer,
  type IsoDate,
  type IsoMonth,
  isParticipantsChallenge,
  type Meal,
  type MetadataHealthDataDebounce,
  type MetadataHealthDataSummary,
  type MetadataMovementStreakDebounce,
  type MovementStreakDocument,
  type Organization,
  type ParticipantsChallenge,
  type PushNotificationBase,
  type QuizStreakDocument,
  type SummaryReport,
  timestampToDate,
  type UserNotificationInfo,
  type UUIDString,
  type WellnessBlog,
  type WellnessBlogCategories,
  type WellnessQuiz,
  type WellnessQuote,
  type Workout,
} from './types';
import {
  challengeGroupParticipantTransformer,
  challengeGroupsTransformer,
  challengeTransformer,
  createBulkAddDocuments,
  createCollectionDocumentCount,
  createDeleteDocumentById,
  createGetDocumentById,
  createGetDocumentExistsById,
  createGetDocumentsByIds,
  createGetDuplicateDocumentsByField,
  createMarkForDeleteById,
  createQueryIterator,
  createQueryIteratorStream,
  createQueryMapper,
  createQueryTransformer,
  createSetDocumentById,
  createUpdateDocumentByIdCallback,
  createUpdateDocumentByIdTransaction,
  createUpdateDocumentRaw,
  createUpdateOnlyDocumentById,
  deleteByQuery,
  filterAppUserChallenges,
  filterByUniqueProperty,
  filterRecentChallenges,
  filterTruthy,
  filterUnique,
  generateIsoDateValuesFromRange,
  getAllFulfilledOrThrow,
  getAppUserWithAdminTimestamps,
  getChallengeWithAdminTimestamps,
  getClientAppUserDTO2,
  getCountDocumentsForQuery,
  getDaysSinceStart,
  getIsoDateDaysBefore,
  getIsoDateDifference,
  isEmptyArray,
  markForDeleteByQuery,
  mergeAndDeduplicateById,
  transformAnyTimestampsToFirebaseAdminTimestamp,
  transformUndefinedToDeleteAndTimestamps,
  uuid,
} from './utils';

/**
 * See FIRESTORE_PATHS map defining all of the firestore document paths in the app
 *  located at `functions/src/firebase.ts`
 */
const appUsersRef = db.collection('appUsers') as CollectionReference<AppUser>;
const algoliaBaseAppUsersRef = db.collection(
  'algoliaBaseAppUsers',
) as CollectionReference<BaseAppUser>;
const appUserNotificationsRef = (userId: UUIDString) =>
  appUsersRef
    .doc(userId)
    .collection('notifications') as CollectionReference<AppUserNotificationDocument>;
const appUserNotificationsDocRef = (userId: UUIDString) =>
  appUserNotificationsRef(userId).doc('default');
export const appUserHealthData = (userId: UUIDString) =>
  appUsersRef.doc(userId).collection('healthData') as CollectionReference<HealthData>;
// /workouts/{workoutId}
const workoutsRef = db.collection('workouts') as CollectionReference<Workout>;
export const workoutRef = (workoutId: UUIDString) => workoutsRef.doc(workoutId);
// /meals/{mealId}
const mealsRef = db.collection('meals') as CollectionReference<Meal>;
const allChallengesRef = db.collection('challenges') as CollectionReference<Challenge>;
// /challenges/{challengeId}/challengeParticipants/{participantId}
const challengeGroupParticipantsRef = (challengeId: UUIDString) =>
  db
    .collection('challenges')
    .doc(challengeId)
    .collection('challengeParticipants') as CollectionReference<ChallengeGroupParticipantDocument>;
export const challengeGroupParticipantsDocRef = (
  challengeId: UUIDString,
  participantId: UUIDString,
) => challengeGroupParticipantsRef(challengeId).doc(participantId);
// /challenges/{challengeId}/challengeParticipants/{participantId}
const challengeParticipantsCollectionGroupRef = db.collectionGroup(
  'challengeParticipants',
) as CollectionGroup<ChallengeGroupParticipantDocument>;
// /challenges/{challengeId}/challengeGroups/{groupId}
const challengeGroupsRef = (challengeId: UUIDString) =>
  db
    .collection('challenges')
    .doc(challengeId)
    .collection('challengeGroups') as CollectionReference<ChallengeGroupDocument>;
export const challengeGroupRef = (challengeId: UUIDString, groupId: UUIDString) =>
  challengeGroupsRef(challengeId).doc(groupId);
const challengeGroupsCollectionGroupRef = db.collectionGroup(
  'challengeGroups',
) as CollectionGroup<ChallengeGroupDocument>;
// /challenges/{challengeId}/challengeGroups/{groupId}/metadata
export const challengeGroupsMetdataRef = (challengeId: UUIDString, groupId: UUIDString) =>
  db
    .collection('challenges')
    .doc(challengeId)
    .collection('challengeGroups')
    .doc(groupId)
    .collection('metadata')
    .doc('default') as DocumentReference<ChallengeGroupDocumentMetadata>;
// /organizations/{organizationId}
const organizationsRef = db.collection('organizations') as CollectionReference<Organization>;
// /summaryReports/{summaryReportId}
const summaryReportsRef = db.collection('summaryReports') as CollectionReference<SummaryReport>;
const wellnessBlogsRef = db.collection('wellnessBlogs') as CollectionReference<WellnessBlog>;
const wellnessQuizRef = db.collection('wellnessQuiz') as CollectionReference<WellnessQuiz>;
const quizStreakRef = db.collection('quizProgress') as CollectionReference<QuizStreakDocument>;
const movementStreaksRef = db.collection(
  'movementStreaks',
) as CollectionReference<MovementStreakDocument>;
const wellnessQuotesRef = db.collection('wellnessQuotes') as CollectionReference<WellnessQuote>;
const authTokensRef = db.collection('authTokens') as CollectionReference<AuthToken>;
const inviteCodesRef = db.collection('inviteCodes') as CollectionReference<InviteCodeDocument>;

export const metadataMovementStreakDebounceDocRef = (userId: UUIDString) =>
  appUsersRef
    .doc(userId)
    .collection('metadata')
    .doc('streakDebounce') as DocumentReference<MetadataMovementStreakDebounce>;
export const metadataHealthDataDebounceDocRef = (userId: UUIDString) =>
  appUsersRef
    .doc(userId)
    .collection('metadata')
    .doc('healthDataDebounce') as DocumentReference<MetadataHealthDataDebounce>;
export const metadataHealthDataSummaryDocRef = (userId: UUIDString) =>
  appUsersRef
    .doc(userId)
    .collection('metadata')
    .doc('healthData') as DocumentReference<MetadataHealthDataSummary>;

/** APP USERS */
export const getAppUsersByIds = createGetDocumentsByIds(appUsersRef);

export const getAppUsersByOrganizationId = async (organizationId: UUIDString) => {
  const query = appUsersRef.where(
    'organizationIds' satisfies keyof AppUser,
    'array-contains',
    organizationId,
  );
  const result = await query.get();
  return result.docs.map(doc => doc.data());
};

export const getAlgoliaBaseAppUsersByIds = createGetDocumentsByIds(algoliaBaseAppUsersRef);
export const getAlgoliaBaseAppUserById = createGetDocumentById(algoliaBaseAppUsersRef);

export const setAlgoliaBaseAppUser = createSetDocumentById(algoliaBaseAppUsersRef);

export const deleteAlgoliaBaseAppUser = createDeleteDocumentById(algoliaBaseAppUsersRef);

export const setAppUserById = createSetDocumentById(appUsersRef);

export const updateAppUserById = createUpdateOnlyDocumentById(
  appUsersRef,
  transformUndefinedToDeleteAndTimestamps,
);

export const updateAppUserByIdTransaction = createUpdateDocumentByIdTransaction(
  appUsersRef,
  transformUndefinedToDeleteAndTimestamps,
);

export const updateAppUserByIdCallback = createUpdateDocumentByIdCallback(
  appUsersRef,
  transformUndefinedToDeleteAndTimestamps,
);

export const updateAppUser = createUpdateDocumentRaw(appUsersRef, getAppUserWithAdminTimestamps);

export const forceUpdateAppUserById = (id: UUIDString) =>
  updateAppUserByIdCallback(id, () => ({lastModifiedDateTime: getNowAsRawTimestampServer()}));

export const deleteAppUserById = createDeleteDocumentById(appUsersRef);

export const deleteAppUsersByIdsQuery = (ids: string[]) => {
  const query = appUsersRef.where(FieldPath.documentId(), 'in', ids);
  return deleteByQuery(query);
};

export const getAppUsersDuplicateByEmail = () =>
  createGetDuplicateDocumentsByField(appUsersRef, 100)('email');

export const getAppUsersDuplicateByExpoPushToken = () =>
  createGetDuplicateDocumentsByField(appUsersRef, 100)('expoPushToken');

export const getAppUserByEmail = async (email: string | undefined, transaction?: Transaction) => {
  if (!email) return;
  const querySnapshot = transaction
    ? await transaction.get(appUsersRef.where('email', '==', email))
    : await appUsersRef.where('email' satisfies keyof AppUser, '==', email).get();
  if (querySnapshot.empty) {
    return;
  }
  // Assuming there is only one user with this email
  const [userDoc] = querySnapshot.docs;

  if (!userDoc?.exists) {
    return;
  }

  return userDoc.data();
};

export const getAppUserByPhoneNumber = async (
  phoneNumber: string | undefined,
  transaction?: Transaction,
) => {
  if (!phoneNumber) return;
  const querySnapshot = transaction
    ? await transaction.get(appUsersRef.where('phoneNumber', '==', phoneNumber))
    : await appUsersRef.where('phoneNumber' satisfies keyof AppUser, '==', phoneNumber).get();
  if (querySnapshot.empty) {
    return;
  }
  // Assuming there is only one user with this email
  const [userDoc] = querySnapshot.docs;

  if (!userDoc?.exists) {
    return;
  }

  return userDoc.data();
};

export const getAppUserById = createGetDocumentById(appUsersRef);
export const getAppUserExists = createGetDocumentExistsById(appUsersRef);

export const getAcceptedAppUsersForChallenge = (
  challenge: ParticipantsChallenge,
  transaction?: Transaction,
): Promise<AppUser[]> => {
  const appUserPromises = challenge.participants
    .filter(p => p.inviteStatus === ChallengeInviteStatus.ACCEPTED)
    .map(p => getAppUserById(p.id, transaction));

  return getAllFulfilledOrThrow(appUserPromises);
};

export const getUsersMovementStreakContinue = async (yesterday: IsoDate) => {
  const querySnapshot = await appUsersRef
    .where('movementStreak.lastCompletedIsoDate', '==', yesterday) // Last completed must be yesterday to continue a streak
    .where('movementStreak.streakStartIsoDate', '!=', yesterday) // Streak start must be more than 1 day before yesterday, indicating it is at least 2 days in a row
    .get();

  return querySnapshot.docs.map(doc => doc.data());
};

export const transformAppUsers = createQueryTransformer(appUsersRef, 10);

export const transformAppUsersLarge = createQueryTransformer(appUsersRef, 100);

export const iterateAppUsers = createQueryIterator(appUsersRef, 10);

export const iterateAppUsersLarge = createQueryIterator(appUsersRef, 100);

export const streamAppUsers = createQueryIteratorStream(appUsersRef);

export const streamMovementStreaks = createQueryIteratorStream(movementStreaksRef);

/** HEALTH DATA */
export const addHealthData = (userId: UUIDString) =>
  createBulkAddDocuments(appUserHealthData(userId));

export const deleteAllHealthData = (userId: UUIDString) => deleteByQuery(appUserHealthData(userId));

export const getAllHealthData = async (userId: UUIDString) => {
  const query = appUserHealthData(userId);
  const snapshot = await query.get();
  return snapshot.docs.map(doc => doc.data());
};

export const getHealthDataForRange = async (
  userId: UUIDString,
  startMonth: IsoMonth,
  endMonth: IsoMonth,
  transaction?: Transaction,
) => {
  const query = appUserHealthData(userId)
    .where(FieldPath.documentId(), '>=', startMonth)
    .where(FieldPath.documentId(), '<=', endMonth);
  const snapshot = await (transaction ? transaction.get(query) : query.get());
  return snapshot.docs.map(doc => doc.data());
};

export const getMetadataHealthDataSummary = async (userId: UUIDString) => {
  const doc = await metadataHealthDataSummaryDocRef(userId).get();
  return doc.data();
};

/** NOTIFICATIONS */
export const getUsersNotificationDoc = async (notifications: PushNotificationBase<object>[]) => {
  const docRefs = notifications.map(async notification => {
    const appUserResponse = await appUsersRef.doc(notification.userId).get();
    const appUser = appUserResponse.data();
    const notificationDocResponse = await appUserNotificationsDocRef(notification.userId).get();
    const notificationDoc = notificationDocResponse.data();
    if (!appUser) {
      LOGGER.error(`User ${notification.userId} not found`);
      return;
    }
    return {
      notification,
      notificationDoc,
      appUser,
    } as UserNotificationInfo;
  });
  const docs = await getAllFulfilledOrThrow(docRefs);

  return docs.filter(filterTruthy);
};

export const updateUserNotificationDocTransaction = (userId: UUIDString) =>
  createUpdateDocumentByIdCallback(
    appUserNotificationsRef(userId) as CollectionReference<DocumentIdTypeDoc>,
  );

/** CHALLENGES */
export const getAppUserParticipantChallenges = async (
  userId: UUIDString | '',
  isRecentChallenge: boolean,
  transaction: Transaction,
) => {
  if (!userId) {
    LOGGER.warn('userId is undefined');
    return;
  }
  const challengeQuery = allChallengesRef.where(
    'participantIds' satisfies keyof Challenge,
    'array-contains',
    userId,
  );
  const challengesSnapshot = await transaction.get(challengeQuery);

  const challenges = challengesSnapshot.docs.map(c => c.data()).filter(isParticipantsChallenge);

  const userChallenges = filterAppUserChallenges(userId, challenges);

  // If not recent challenge, return all user challenges
  if (!isRecentChallenge) return userChallenges;

  const today = new Date();
  return filterRecentChallenges(userChallenges, today);
};

export const getChallengeById = createGetDocumentById(allChallengesRef);

export const getParticipantChallengeById = createGetDocumentById<ParticipantsChallenge>(
  // @ts-ignore -- enforced by where clause
  allChallengesRef,
  undefined,
  q => q.where('groupingType', 'in', CHALLENGE_PARTICIPANTS_GROUPING_TYPES),
);

export const getGroupsChallengeById = createGetDocumentById<GroupsChallenge>(
  // @ts-ignore -- enforced by where clause
  allChallengesRef,
  undefined,
  q => q.where('groupingType', '==', ChallengeGroupingType.GROUPS),
);

export const getAllActiveParticipantChallenges = async (currentDate: Date) => {
  const currentDateAdmin = FirebaseAdminTimestamp.fromDate(currentDate);
  const querySnapshot = await allChallengesRef
    .where('groupingType' satisfies keyof Challenge, 'in', CHALLENGE_PARTICIPANTS_GROUPING_TYPES)
    .where('startedDateTime' satisfies keyof Challenge, '<=', currentDateAdmin)
    .where('endedDateTime' satisfies keyof Challenge, '>=', currentDateAdmin)
    .get();
  return querySnapshot.docs.map(doc => doc.data() as ParticipantsChallenge);
};

export const getChallengesEndedLast15Minutes = async () => {
  // Look for challenges that ended in  the last 15 minutes
  const lastRun = FirebaseAdminTimestamp.fromMillis(Date.now() - Number(15 * MS_1_MINUTE));
  const now = FirebaseAdminTimestamp.now();
  const querySnapshot = await allChallengesRef
    .where('startedDateTime' satisfies keyof Challenge, '<=', now) // has started
    .where('endedDateTime' satisfies keyof Challenge, '>', lastRun) // ended after lastRun
    .where('endedDateTime' satisfies keyof Challenge, '<=', now) // ended before now (within last 15 min)
    .get();
  return (
    querySnapshot.docs
      .map(doc => doc.data())
      // has not yet sent notifications
      .filter(c => !c.isChallengeEndNotificationSent)
  );
};

export const updateChallengeRaw = createUpdateDocumentRaw(
  allChallengesRef,
  getChallengeWithAdminTimestamps,
);

export const updateChallengeById = createUpdateDocumentByIdTransaction(
  allChallengesRef,
  challengeTransformer,
);

export const updateChallengeByIdTransaction = createUpdateDocumentByIdCallback(
  allChallengesRef,
  challengeTransformer,
);

export const countChallenges = createCollectionDocumentCount(allChallengesRef);

export const transformChallengeGroupParticipants = (challengeId: UUIDString) =>
  createQueryTransformer(challengeGroupParticipantsRef(challengeId), 100);

/** GROUP CHALLENGES */
export const getAllActiveGroupChallenges = async (currentDate: Date) => {
  const currentDateAdmin = FirebaseAdminTimestamp.fromDate(currentDate);
  const sevenDaysAgoAdmin = FirebaseAdminTimestamp.fromDate(
    new Date(currentDate.getTime() - (7 * 24 * 60 * 60 * 1000)),
  );
  const querySnapshot = await allChallengesRef
    .where('groupingType', '==', ChallengeGroupingType.GROUPS)
    .where('startedDateTime' satisfies keyof Challenge, '<=', currentDateAdmin)
    .where('endedDateTime' satisfies keyof Challenge, '>=', sevenDaysAgoAdmin)
    .get();
  const challenges = querySnapshot.docs.map(doc => doc.data() as GroupsChallenge);

  // Ensure no draft challenges
  return challenges.filter(c => c.flags?.isDraft !== true);
};

export const getChallengeGroupParticipantsById = async (
  challengeIds: UUIDString[],
  userId: UUIDString,
) => {
  const querySnapshot = await challengeParticipantsCollectionGroupRef
    .where('challengeId' satisfies keyof ChallengeGroupParticipantDocument, 'in', challengeIds)
    .where('id' satisfies keyof ChallengeGroupParticipantDocument, '==', userId)
    .where(
      'inviteStatus' satisfies keyof ChallengeGroupParticipantDocument,
      '==',
      ChallengeInviteStatus.ACCEPTED,
    )
    .get();
  return querySnapshot.docs.map(doc => ({
    ...doc.data(),
    ref: doc.ref,
  }));
};

export const getChallengeGroupParticipantDocumentById = (challengeId: UUIDString) =>
  createGetDocumentById(challengeGroupParticipantsRef(challengeId));

export const getChallengeGroupParticipants = async (challengeId: UUIDString) => {
  const querySnapshot = await challengeParticipantsCollectionGroupRef
    .where('challengeId' satisfies keyof ChallengeGroupParticipantDocument, '==', challengeId)
    .get();
  return querySnapshot.docs.map(doc => doc.data());
};

export const getChallengeGroupParticipantsPendingForChallengeStream = (challengeId: UUIDString) =>
  createQueryMapper(
    challengeParticipantsCollectionGroupRef
      .where(
        'inviteStatus' satisfies keyof ChallengeGroupParticipantDocument,
        '==',
        ChallengeInviteStatus.PENDING,
      )
      .where('challengeId' satisfies keyof ChallengeGroupParticipantDocument, '==', challengeId),
  );

export const getChallengeAcceptedGroupParticipants = async (
  challengeId: UUIDString,
  groupId?: UUIDString | null,
  transaction?: Transaction,
) => {
  let query = challengeGroupParticipantsRef(challengeId).where(
    'inviteStatus' satisfies keyof ChallengeGroupParticipantDocument,
    '==',
    ChallengeInviteStatus.ACCEPTED,
  );

  // Add additional groupId filter if provided
  if (groupId) {
    query = query.where(
      'groupIds' satisfies keyof ChallengeGroupParticipantDocument,
      'array-contains',
      groupId,
    );
  }

  const querySnapshot = transaction ? await transaction.get(query) : await query.get();
  return querySnapshot.docs.map(doc => doc.data());
};

export const getChallengeGroupDocumentById = (challengeId: UUIDString) =>
  createGetDocumentById(challengeGroupsRef(challengeId));

export const getChallengeGroupParticipantCountInternal = async (
  challengeId: UUIDString,
  groupId: UUIDString,
) =>
  getCountDocumentsForQuery(
    challengeGroupParticipantsRef(challengeId).where(
      'groupIds' satisfies keyof ChallengeGroupParticipantDocument,
      'array-contains',
      groupId,
    ),
  );

export const getChallengeGroupCaptain = async (challengeId: UUIDString, groupId: UUIDString) => {
  const querySnapshot = await challengeGroupParticipantsRef(challengeId)
    .where(
      'groupIds' satisfies keyof ChallengeGroupParticipantDocument,
      'array-contains',
      groupId,
    )
    .where('role' satisfies keyof ChallengeGroupParticipantDocument, '==', ChallengeParticipantRole.CAPTAIN)
    .get();
  return querySnapshot.docs.map(doc => doc.data())[0];
};

export const getChallengeGroupDocumentsByIds = (challengeId: UUIDString) =>
  createGetDocumentsByIds(challengeGroupsRef(challengeId));

export const getChallengeAllGroupIds = async (challengeId: UUIDString) => {
  const snapshot = await challengeGroupsRef(challengeId).get();
  return snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      groupId: data.id,
      challengeId: data.challengeId,
    };
  });
};

export const getChallengeAllGroupForLevel = async (
  challengeId: UUIDString,
  level: number,
  transaction?: Transaction,
) => {
  const query = challengeGroupsRef(challengeId).where('level', '==', level);
  const snapshot = await (transaction ? transaction.get(query) : query.get());
  return snapshot.docs.map(doc => doc.data());
};

export const getChallengeAllGroupIdsForLevel = async (
  challengeId: UUIDString,
  level: number,
  transaction?: Transaction,
) => {
  const snapshot = await getChallengeAllGroupForLevel(challengeId, level, transaction);
  return snapshot.map(doc => ({
    groupId: doc.id,
    challengeId: doc.challengeId,
  }));
};

export const getChallengeGroupChildren = async (
  challengeId: UUIDString,
  groupId: UUIDString,
  transaction?: Transaction,
) => {
  const query = challengeGroupsRef(challengeId).where(
    'parentGroupId' satisfies keyof ChallengeGroupDocument,
    '==',
    groupId,
  );
  const snapshot = await (transaction ? transaction.get(query) : query.get());
  return snapshot.docs.map(doc => ({...doc.data(), ref: doc.ref}));
};

export const getChallengeGroupDocumentsForAggregation = async (challengeIds: UUIDString[]) => {
  const now = FirebaseAdminTimestamp.now();
  const q1 = challengeGroupsCollectionGroupRef
    .where('aggregationConfig.mode', '==', 'BATCHED')
    .where('aggregationConfig.nextAggregationDateTime', '<=', now)
    .where('challengeId', 'in', challengeIds)
    .get();

  const q2 = challengeGroupsCollectionGroupRef
    .where('aggregationConfig.mode', '==', 'BATCHED')
    .where('aggregationConfig.nextAggregationDateTime', '==', null)
    .where('challengeId', 'in', challengeIds)
    .get();

  const [snap1, snap2] = await getAllFulfilledOrThrow([q1, q2]);

  // Combine and remove duplicates if necessary
  const combinedDocs = mergeAndDeduplicateById(snap1!, snap2!);

  return combinedDocs.map(doc => {
    const data = doc.data();
    return {
      groupId: data.id,
      challengeId: data.challengeId,
    };
  });
};

export const transformAllChallengeGroupDocumentsForTypeMigration = (challengeId: UUIDString) =>
  createQueryTransformer(
    challengeGroupsCollectionGroupRef
      .where('aggregationConfig.mode', '==', 'BATCHED')
      .where('challengeId', '==', challengeId),
    10,
  );

export const getChallengeGroupMetadataDocumentById = async (
  challengeId: UUIDString,
  groupId: UUIDString,
  transaction: Transaction,
) => {
  const snapshot = await transaction.get(challengeGroupsMetdataRef(challengeId, groupId));
  return snapshot.data();
};

export const updateChallengeGroupDocumentById = (challengeId: UUIDString) =>
  createUpdateDocumentByIdTransaction(challengeGroupsRef(challengeId), challengeGroupsTransformer);

export const updateChallengeGroupParticipantById = (challengeId: UUIDString) =>
  createUpdateDocumentByIdTransaction(
    challengeGroupParticipantsRef(challengeId),
    challengeGroupParticipantTransformer,
  );

/** WORKOUTS */
export const deleteWorkoutById = createDeleteDocumentById(workoutsRef);

export const getWorkoutById = createGetDocumentById(workoutsRef);

export const getChildWorkoutsByParentWorkoutId = async (workoutId: UUIDString) => {
  const querySnapshot = await workoutsRef
    .where('parentWorkoutId' satisfies keyof Workout, '==', workoutId)
    .get();
  return querySnapshot.docs.map(doc => doc.data());
};

const getUserParticipantWorkouts = async (userId: UUIDString, transaction?: Transaction) => {
  const query = workoutsRef.where(
    'participantIds' satisfies keyof Workout,
    'array-contains',
    userId,
  );
  const snapshot = await (transaction ? transaction.get(query) : query.get());

  return snapshot.docs.map(doc => doc.data());
};

export const getUserTrainerWorkouts = async (userId: UUIDString, transaction?: Transaction) => {
  const query = workoutsRef.where('trainerIds' satisfies keyof Workout, 'array-contains', userId);
  const snapshot = await (transaction ? transaction.get(query) : query.get());

  return snapshot.docs.map(doc => doc.data());
};

export const getAllAppUserWorkouts = async (userId: UUIDString, transaction?: Transaction) => {
  const participantWorkouts = await getUserParticipantWorkouts(userId, transaction);
  const trainerWorkouts = await getUserTrainerWorkouts(userId, transaction);
  return [...participantWorkouts, ...trainerWorkouts].filter(filterByUniqueProperty('id'));
};

export const getAppUserCompletedWorkoutsForRange = async (
  userId: UUIDString,
  interval: DateInterval,
) => {
  const query = workoutsRef
    .where('trainerIds' satisfies keyof Workout, 'array-contains', userId)
    .where('startedDateTime' satisfies keyof Workout, '>=', interval.startDate)
    .where('startedDateTime' satisfies keyof Workout, '<=', interval.endDate);
  const snapshot = await query.get();
  return (
    snapshot.docs
      .map(doc => doc.data())
      // Must filter the participantIds as well since need to check multiple array-contains
      .filter(w => w.isCompleted && w.participantIds.includes(userId))
  );
};

// Get all workouts where the user is a participant & trainer (their own workout), and is completed
export const getAllUserParticipatingCompleteWorkouts = async (userId: UUIDString) => {
  const workouts = await getUserParticipantWorkouts(userId);

  return workouts.filter(w => w.isCompleted && w.trainerIds.includes(userId));
};

export const getAllTrainerWorkoutsExceptSelf = async (trainerId: UUIDString) => {
  const allTrainerWorkouts = await getUserTrainerWorkouts(trainerId);
  return allTrainerWorkouts
    .filter(w => {
      const isTrainerOnlyParticipant = w.participantIds[0] === trainerId;
      return !isTrainerOnlyParticipant;
    })
    .filter(w => w.isCompleted);
};

export const countAllUserCompleteWorkouts = async (userId: UUIDString) => {
  const querySnapshot = await workoutsRef.where('trainerIds', 'array-contains', userId).get();

  const workouts = querySnapshot.docs.filter(doc => {
    const workout = doc.data();
    return workout.participantIds.includes(userId) && workout.isCompleted === true;
  });

  return workouts.length;
};

/** MEALS */

export const countAllUserMeals = async (userId: UUIDString) => {
  const snapshot = await mealsRef
    .where('userId' satisfies keyof Meal, '==', userId)
    .count()
    .get();

  return snapshot.data().count;
};

/** ORGANIZATIONS */
export const getOrganizationById = createGetDocumentById(organizationsRef);

export const getOrganizationsByIds = createGetDocumentsByIds(organizationsRef);

export const getAppUserOrganizations = (appUser: AppUser, transaction: Transaction) => {
  const {organizationIds} = appUser;
  if (!organizationIds || isEmptyArray(organizationIds)) return;
  return getOrganizationsByIds(organizationIds, transaction);
};

export const updateOrganizationByIdCallback = createUpdateDocumentByIdCallback(
  organizationsRef,
  transformUndefinedToDeleteAndTimestamps,
);

export const updateOrganizationByIdTransaction = createUpdateDocumentByIdTransaction(
  organizationsRef,
  transformUndefinedToDeleteAndTimestamps,
);

export const addUserToOrganization = (organization: Organization, appUser: AppUser) => {
  if (isAppUserAdmin(appUser) && !organization.adminIds.includes(appUser.id)) {
    LOGGER.debug(`Adding ${appUser.id} to organization ${organization.id} as admin`);
    return {...organization, adminIds: [...organization.adminIds, appUser.id]};
  } else if (isAppUserTrainer(appUser) && !organization.coachIds.includes(appUser.id)) {
    LOGGER.debug(`Adding ${appUser.id} to organization ${organization.id} as coach`);
    return {...organization, coachIds: [...organization.coachIds, appUser.id]};
  } else if (isAppUserClient(appUser) && !organization.clientIds.includes(appUser.id)) {
    LOGGER.debug(`Adding ${appUser.id} to organization ${organization.id} as client`);
    return {...organization, clientIds: [...organization.clientIds, appUser.id]};
  }
  return organization;
};

/** SUMMARY REPORTS */
export const getSummaryReportById = createGetDocumentById(summaryReportsRef);

export const updateSummaryReportByIdTransaction = createUpdateDocumentByIdCallback(
  summaryReportsRef,
  transformUndefinedToDeleteAndTimestamps,
);

/** CLIENT DATA */
export const getAllTrainerClientsInOrgs2 = async (trainerAppUser: AppUser) => {
  if (isEmptyArray(trainerAppUser.organizationIds)) return [];

  // First collect all clients for all of the organizations the trainer belongs in
  const clientIdPromises = trainerAppUser.organizationIds.map(async orgId => {
    const org = await getOrganizationById(orgId);
    return org?.clientIds ?? [];
  });

  const clientIdArrays = await getAllFulfilledOrThrow(clientIdPromises);
  const clientIds = clientIdArrays.flat().filter(filterUnique);
  LOGGER.debug(`Fetched ${clientIds.length} client ids for trainer ${trainerAppUser.id}`);

  // For each client id, get the app user and get the summary DTO for that app user
  let usersProcessed = 0;
  const appUserPromises = clientIds.map(async clientId => {
    const appUser = await getAppUserById(clientId);
    if (!appUser) return;
    const startTimeMs = Date.now();
    const result = await getClientAppUserDTO2(appUser);
    const endTimeMs = Date.now();
    usersProcessed++;
    LOGGER.debug(
      `Processed ${usersProcessed} of ${clientIds.length} users for client ${clientId} in ${(endTimeMs - startTimeMs) / 1000}s`,
    );
    return result;
  });

  // Return all of the app user DTOs
  return getAllFulfilledOrThrow(appUserPromises);
};

/** WELLNESS BLOGS */
export const getWellnessBlogs = async (
  disabledCategories: WellnessBlogCategories[],
  transaction?: Transaction,
) => {
  const querySnapshot = transaction
    ? await transaction.get(wellnessBlogsRef)
    : await wellnessBlogsRef.get();

  // Filter out documents where 'type' is in the disabledCategories list
  return querySnapshot.docs
    .filter(doc => {
      const data = doc.data();
      return !disabledCategories.includes(data.type);
    })
    .map(doc => doc.data())
    .sort((a, b) => a.order - b.order);
};

/** WELLNESS QUIZ */

/**
 * Gets a wellness blog by date offset using IsoDate format
 * This is a separate implementation to support the transition to IsoDate format
 */
export const getWellnessBlogByIsoDateOffset = async (
  isoDate: IsoDate,
  disabledCategories: WellnessBlogCategories[],
  transaction?: Transaction,
) => {
  // Get all blogs first
  const allWellnessBlogs = await getWellnessBlogs(disabledCategories, transaction);

  // Calculate the offset using IsoDate difference
  const daysSinceStart = getIsoDateDifference(isoDate, DEFAULT_START_ISO_DATE, true);

  // Ensure the offset wraps
  const index = daysSinceStart % allWellnessBlogs.length;

  // Return the blog at the calculated index
  return allWellnessBlogs[index];
};

//! DEPRECATED: in favor of `getWellnessQuizDocumentByIsoDateOffset`
export const getWellnessQuizDocumentByOffset = async (date: Date) => {
  // Get collection size
  const collectionSizeSnapshot = await wellnessQuizRef.count().get();
  const collectionSize = collectionSizeSnapshot.data().count;

  // Get the offset index
  const daysSinceStart = getDaysSinceStart(DEFAULT_QUIZ_START_DATE_OFFSET, date);
  // Ensure the offset wraps
  const offset = daysSinceStart % collectionSize;

  // Fetch one document of that offset
  const querySnapshot = await wellnessQuizRef
    .orderBy('order')
    .offset(offset)
    .limit(1) // Fetch just one document
    .get();

  return querySnapshot.docs[0]?.data();
};

/**
 * Gets a wellness quiz document by offset using IsoDate format
 * This is a separate implementation from getWellnessQuizDocumentByOffset to support
 * the transition to IsoDate format
 */
export const getWellnessQuizDocumentByIsoDateOffset = async (isoDate: IsoDate) => {
  // Get collection size
  const collectionSizeSnapshot = await wellnessQuizRef.count().get();
  const collectionSize = collectionSizeSnapshot.data().count;

  // Get the offset index using IsoDate difference
  const daysSinceStart = getIsoDateDifference(isoDate, DEFAULT_QUIZ_START_ISO_DATE, true);
  // Ensure the offset wraps
  const offset = daysSinceStart % collectionSize;

  // Fetch one document of that offset
  const querySnapshot = await wellnessQuizRef
    .orderBy('order')
    .offset(offset)
    .limit(1) // Fetch just one document
    .get();

  return querySnapshot.docs[0]?.data();
};

/** QUIZ PROGRESS */
export const getAllQuizProgressForUser = async (userId: UUIDString) => {
  const querySnapshot = await quizStreakRef
    .where('userId', '==', userId)
    .orderBy('completedIsoDate', 'desc')
    .get();
  if (querySnapshot.empty) return;
  return querySnapshot.docs.map(doc => doc.data());
};

export const getQuizStreakContinueAppUsers = async (yesterday: IsoDate) => {
  const usersCompletedYesterday = await appUsersRef
    .where('quizStreak.lastCompletedIsoDate', '==', yesterday)
    .get();
  return usersCompletedYesterday.docs.map(doc => doc.data());
};

export const getQuizStreakStartAppUsers7Days = async (today: IsoDate, yesterday: IsoDate) => {
  const endRange = getIsoDateDaysBefore(yesterday, 1);
  const startRange = getIsoDateDaysBefore(endRange, 6);
  const isoDates30Days = generateIsoDateValuesFromRange(startRange, endRange);
  const querySnapshot = await appUsersRef
    .where('quizStreak.lastCompletedIsoDate', 'in', isoDates30Days)
    .get();
  return querySnapshot.docs.map(doc => doc.data());
};

/** MOVEMENT STREAK */
export const getAllMovementStreaksForUser = async (
  userId: UUIDString,
  transaction?: Transaction,
) => {
  const query = movementStreaksRef.where(
    'userId' satisfies keyof MovementStreakDocument,
    '==',
    userId,
  );
  const querySnapshot = transaction ? await transaction.get(query) : await query.get();
  return querySnapshot.docs.map(doc => doc.data());
};

export const getMostRecentMovementStreakDocument = async (userId: string) => {
  const querySnapshot = await movementStreaksRef
    .where('userId', '==', userId) // Filter by the specific user ID
    .orderBy('completedIsoDate', 'desc') // Sort by isoDate in descending order
    .limit(1) // Get the most recent document
    .get();

  return querySnapshot.docs.map(doc => doc.data())[0];
};

export const getMovementStreakById = createGetDocumentById(movementStreaksRef);

export const updateMovementStreakById = createUpdateDocumentByIdTransaction(
  movementStreaksRef,
  transformUndefinedToDeleteAndTimestamps,
);

export const markMovementStreakForDeletion = createMarkForDeleteById(movementStreaksRef, {
  isSkipDeleteHook: false,
});

export const deleteMovementStreakById = createDeleteDocumentById(movementStreaksRef);

export const markMovementStreaksForDeletionForUser = (userId: UUIDString) => {
  const query = movementStreaksRef.where(
    'userId' satisfies keyof MovementStreakDocument,
    '==',
    userId,
  );
  return markForDeleteByQuery(query, {isSkipDeleteHook: true});
};

/** WELLNESS QUOTES */
export const getWellnessQuotesData = async () => {
  const querySnapshot = await wellnessQuotesRef.get();
  const quotes = querySnapshot.docs.map(doc => doc.data());
  return quotes.sort((a, b) => a.order - b.order);
};

/** AUTH TOKENS  */
const setAuthToken = createSetDocumentById(
  authTokensRef,
  transformAnyTimestampsToFirebaseAdminTimestamp,
);

export const generateAndSaveAuthToken = async (userId: UUIDString) => {
  const token = uuid();
  const expiresDateTime = dateToRawTimestamp(new Date(Date.now() + Number(7 * MS_24_HOURS)));
  await setAuthToken(userId, {token, expiresDateTime, userId});
  return token;
};

const getAuthToken = createGetDocumentById(authTokensRef);

export const verifyIdToken = async (token: string, userId: UUIDString) => {
  const databaseToken = await getAuthToken(userId);
  if (!databaseToken) return false;
  const expiredDate = timestampToDate(databaseToken.expiresDateTime);
  const isExpired = expiredDate < new Date();
  if (isExpired) return false;

  return databaseToken.token === token;
};

export const deleteAuthToken = createDeleteDocumentById(authTokensRef);

export const getCustomAuthToken = async (firebaseIdToken: string) => {
  try {
    const decodedToken = await auth.verifyIdToken(firebaseIdToken);

    // Generate a custom token to reauthenticate in the old system
    return await auth.createCustomToken(decodedToken.uid);
  } catch {
    return;
  }
};

/** INVITE CODES */
export const getInviteCodeById = createGetDocumentById(inviteCodesRef);

/* eslint-enable max-lines  */
