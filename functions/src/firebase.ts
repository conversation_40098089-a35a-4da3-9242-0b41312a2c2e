import {setupGlobalEnvironment} from './utils/setup/setupGlobalEnvironment';

// Due to some the side-effects of initializing the environment, and guaranteeing correct
// order of dependencies in the bundled output, we need to call a function that returns
// all of our "global" variables so that we can guarantee ALL of them are initialized and
// ready to be used regardless of the order of the imports or entry point location.
export const {
  app,
  auth,
  authV1,
  db,
  getActionCodeSettings,
  getAlgoliaClient,
  getAppLinkUrl,
  getExpoSdk,
  getResend,
  isProd,
  LOGGER,
  onDocumentWritten,
  onFirebaseRequest,
  onSchedule,
  onTaskDispatched,
  projectId,
  storage,
} = setupGlobalEnvironment();

export type TaskDispatchHandlerRequest = Parameters<Parameters<typeof onTaskDispatched>[1]>[0];

export const FIRESTORE_PATHS = {
  algoliaBaseAppUsers: 'algoliaBaseAppUsers/{appUserId}',
  appConfig: 'appConfig/default',
  appUpdates: 'appUpdates/{android|ios}',
  appUsers: 'appUsers/{appUserId}',
  appUserHealthData: 'appUsers/{appUserId}/healthData/{isoMonth}',
  appUserNotificationDocument: 'appUsers/{appUserId}/notifications/default',
  authTokens: 'authTokens/{userId}',
  challenges: 'challenges/{challengeId}',
  challengeGroups: 'challenges/{challengeId}/challengeGroups/{groupId}',
  challengeGroupPosts: 'challenges/{challengeId}/challengeGroups/{groupId}/posts/{postId}',
  challengeGroupPostsGlobal: 'challenges/{challengeId}/globalPosts/{postId}',
  challengeGroupParticipants: 'challenges/{challengeId}/challengeParticipants/{participantId}',
  challengePostsTeams: 'challenges/{challengeId}/posts/{postId}',
  challengePostsGroups: 'challenges/{challengeId}/challengeGroups/{groupId}/posts/{postId}',
  inviteCodes: 'inviteCodes/{inviteCodeId}',
  meals: 'meals/{mealId}',
  movementStreaks: 'movementStreaks/{movementStreakId}',
  organizations: 'organizations/{organizationId}',
  quizProgress: 'quizProgress/{quizStreakId}',
  summaryReports: 'summaryReports/{summaryReportId}',
  wellnessBlogs: 'wellnessBlogs/{blogId}',
  wellnessQuiz: 'wellnessQuiz/{quizId}',
  wellnessQuotes: 'wellnessQuotes/{quoteId}',
  workouts: 'workouts/{workoutId}',
} as const;
