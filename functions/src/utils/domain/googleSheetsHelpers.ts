import {google} from 'googleapis';
import type {sheets_v4 as GoogleSheetsTypes} from 'googleapis';
import {LOGGER} from '../../firebase';

const getGoogleSheetsInstance = () => {
  // LOGGER.debug('Authenticating with Google Auth');
  const auth = new google.auth.GoogleAuth({
    scopes: ['https://www.googleapis.com/auth/spreadsheets'],
  });

  return google.sheets({version: 'v4', auth});
};

const addOrRecreateSheet = async (
  sheets: GoogleSheetsTypes.Sheets,
  spreadsheetId: string,
  sheetTitle: string,
) => {
  // Get the current sheets in the spreadsheet
  const response = await sheets.spreadsheets.get({spreadsheetId});
  const existingSheet = response.data.sheets?.find(sheet => sheet.properties?.title === sheetTitle);

  // If the sheet exists, delete it
  if (existingSheet) {
    LOGGER.debug(`Sheet with name of '${sheetTitle}' already exists. Deleting...`);
    const sheetId = existingSheet.properties?.sheetId;
    if (!sheetId) throw new Error(`Failed to retrieve sheetId for '${sheetTitle}'`);

    await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [
          {
            deleteSheet: {
              sheetId,
            },
          },
        ],
      },
    });
    LOGGER.debug(`Sheet with name of '${sheetTitle}'successfully deleted`);
  }

  // Add a new sheet with the same title
  LOGGER.debug(`Creating new sheet'${sheetTitle}'`);
  const newSheetResponse = await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        {
          addSheet: {
            properties: {
              title: sheetTitle,
              index: 0,
            },
          },
        },
      ],
    },
  });

  const sheetId = newSheetResponse.data.replies?.[0]?.addSheet?.properties?.sheetId;

  if (!sheetId) {
    throw new Error('Failed to create a new sheet.');
  }

  LOGGER.debug(`New sheet '${sheetTitle}' created.`);

  return sheetId;
};

const writeDataToSheet = async (
  sheets: GoogleSheetsTypes.Sheets,
  spreadsheetId: string,
  sheetId: number,
  sheetTitle: string,
  data: string[][],
) => {
  LOGGER.debug(`Writing data to sheet: ${sheetTitle}`);
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: `${sheetTitle}!A1`,
    valueInputOption: 'RAW',
    requestBody: {
      values: data,
    },
  });

  LOGGER.debug(`Data successfully written to sheet: ${sheetTitle}`);

  // Create a filter for the data range
  const filterRange = {
    sheetId,
    startRowIndex: 0, // Start at the first row
    startColumnIndex: 0, // Start at the first column
    endRowIndex: data.length, // Total number of rows
    endColumnIndex: data[0]!.length, // Total number of columns
  };

  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        {
          setBasicFilter: {
            filter: {
              range: filterRange,
            },
          },
        },
      ],
    },
  });

  LOGGER.log(`Filter applied to sheet: ${sheetTitle}`);
};

export const appendToGoogleSheet = async (
  data: string[][],
  spreadsheetId: string,
  sheetTitle: string,
) => {
  const sheets = getGoogleSheetsInstance();

  // LOGGER.debug(`Appending data to sheet: ${sheetTitle}`);

  // Use append method to add data to the next available row
  await sheets.spreadsheets.values.append({
    spreadsheetId,
    range: `${sheetTitle}!A:A`, // Start from column A, Google Sheets will find the next empty row
    valueInputOption: 'RAW',
    insertDataOption: 'INSERT_ROWS',
    requestBody: {
      values: data,
    },
  });

  // LOGGER.debug(`Data successfully appended to sheet: ${sheetTitle}`);
};

export const writeToGoogleSheets = async (
  data: string[][],
  spreadsheetId: string,
  sheetTitle: string,
) => {
  const sheets = getGoogleSheetsInstance();

  const sheetId = await addOrRecreateSheet(sheets, spreadsheetId, sheetTitle);
  await writeDataToSheet(sheets, spreadsheetId, sheetId, sheetTitle, data);
};
