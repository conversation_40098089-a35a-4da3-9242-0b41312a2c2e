{"name": "fly-fit", "version": "1.20.23", "main": "index.js", "private": true, "engineStrict": true, "sideEffects": false, "type": "module", "packageManager": "npm@11.3.0", "repository": {"type": "git", "url": "https://github.com/FlyBodies/fly-fit.git"}, "publishConfig": {"access": "restricted"}, "engines": {"node": ">=22", "npm": ">=10", "pnpm": "please-use-npm", "yarn": "please-use-npm"}, "scripts": {"install:all": "npx concurrently -c auto \"npm i\" \"cd functions && npm i\" \"cd functions/report-generation && npm i\" \"cd functions/swagger-docs && npm i\" \"cd web-sign-up && npm i\"", "dev": "concurrently -c auto -i \"npm:start\" \"npm:dev:firebase\"", "dev:firebase": "cd functions && npm run build && cd .. &&  firebase emulators:start --only functions --project fly-fit-dev", "serve": "cd functions && npm run serve", "start:base": "cross-env EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 EXPO_UNSTABLE_ATLAS=true expo start --go -c -p 8081", "prestart": "npm run env:local:dev", "start": "npm run start:base", "start:dev": "npm run start", "start:debug": "cross-env EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 expo start --go -c", "start:tunnel": "cross-env EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 EXPO_TUNNEL_SUBDOMAIN=fly-fit  expo start --go -c --tunnel", "prestart:prod": "npm run env:local:prod", "start:prod": "npm run start:base", "android:base": "EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 npx expo run:android", "android": "npm run android:dev", "android:dev": "APP_ENV=development npm run env:local:dev && npm run android:base", "android:dev:device": "APP_ENV=development npm run env:local:dev && EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 npx expo run:android --device N156DL", "android:prod": "APP_ENV=production npm run env:local:prod && npm run android:base", "android:prod:device": "APP_ENV=production npm run env:local:prod && EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 npx expo run:android --device N156DL", "ios:base": "EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 npx expo run:ios", "ios": "npm run ios:dev", "ios:dev": "EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 APP_ENV=development npm run env:local:dev && npm run ios:base", "ios:dev:device": "npm run env:local:dev && EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 APP_ENV=development npx expo run:ios --device 00008110-00016DC92E91401E", "ios:dev:release": "npm run env:local:dev && EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 npx expo run:ios --configuration Release", "ios:dev:release:device": "npm run env:local:dev && EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 npx expo run:ios --configuration Release --device 00008110-00016DC92E91401E", "ios:prod": "npm run env:local:prod && EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 APP_ENV=production npx expo run:ios", "ios:prod:device": "npm run env:local:prod && EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 APP_ENV=production npx expo run:ios --device 00008110-00016DC92E91401E", "ios:prod:release": "npm run env:local:prod && EXPO_NO_CLIENT_ENV_VARS=1 EXPO_NO_TYPESCRIPT_SETUP=1 APP_ENV=production npx expo run:ios --configuration release --device 00008110-00016DC92E91401E", "prelint": "cd functions/report-generation && npm run prelint", "lint": "node --max-old-space-size=8192 ./node_modules/.bin/eslint --cache --fix .", "prelint:ci": "cd functions/report-generation && npm run build", "lint:ci": "concurrently -g -c auto \"npm:lint:ci:*\"", "lint:ci:1": "node --max-old-space-size=8192 ./node_modules/.bin/eslint --fix --max-warnings=0 \"./src\"", "lint:ci:2": "node --max-old-space-size=8192 ./node_modules/.bin/eslint --fix --max-warnings=0 \"./functions\"", "lint:ci:3": "node ./node_modules/.bin/eslint --fix --max-warnings=0 \"./web-sign-up\"", "lint:ci:4": "node ./node_modules/.bin/eslint --fix --max-warnings=0 \"**/*.{js,mjs,cjs}\"", "lint:ci-all": "node --max-old-space-size=8192 ./node_modules/.bin/eslint --fix --max-warnings=0 .", "lint:debug": "cross-env TIMING=1 node --max-old-space-size=8192 ./node_modules/.bin/eslint --fix --max-warnings=0 --cache .", "knip": "npx knip --fix --cache", "typecheck": "node --stack-size=65500 ./node_modules/.bin/tsc --project tsconfig.json", "typecheck:ci": "node --stack-size=65500 ./node_modules/.bin/tsc --incremental false --project tsconfig.json", "typecheck:debug": "tsc --explainFiles --extendedDiagnostics --project tsconfig.json", "test:frontend": "jest --config jest.config.cjs", "test:backend": "cd functions && npm run test:ci", "test": "jest", "test:deprecated": "concurrently -g -c auto npm:test:frontend npm:test:backend", "test:watch": " jest --config jest.config.cjs --watch", "test:coverage": "jest --config jest.config.cjs --coverage src/utils", "test:only": "jest --watch --config jest.config.cjs src/types/dates.test.ts", "test:ci": "concurrently -g -c auto npm:doctor npm:typecheck:ci npm:lint npm:test", "import:users": "node ./scripts/import-users.zx.mjs --env dev --file ./scripts/users.source.json", "import:generate-mock": "node ./scripts/import-generate-mock-data.zx.mjs", "import:import-mock": "node ./scripts/import-mock-data.zx.mjs", "cloc": "cloc . --exclude-dir=node_modules,build,dist,.next --include-lang=TypeScript,JavaScript,TSX", "ci:sync": "firebase firestore:indexes --project fly-fit-dev > firestore.indexes.json", "ci:sync:deploy": "firebase deploy --only firestore:indexes --project fly-fit", "ci-cd:local": "node ./scripts/ci-cd.zx.mjs", "ci-cd:local:update": "node ./scripts/ci-cd-update.zx.mjs", "ci-cd:local:rollback": "eas update:rollback", "env:local:prod": "export DOTENV_KEY=$(npx dotenv-vault@latest keys production) && dotenv-vault decrypt $DOTENV_KEY > .env", "env:local:dev": "export DOTENV_KEY_DEV=$(npx dotenv-vault@latest keys development) && dotenv-vault decrypt $DOTENV_KEY_DEV > .env", "env:ci:prod": "npx dotenv-vault decrypt $DOTENV_KEY > .env", "env:ci:dev": "npx dotenv-vault decrypt $DOTENV_KEY_DEV > .env", "env:local-cli:dev": "echo 'MUST RUN MANUALLY' && export DOTENV_KEY_DEV=$(npx dotenv-vault@latest keys development)", "env:local-cli:prod": "echo 'MUST RUN MANUALLY' && export DOTENV_KEY=$(npx dotenv-vault@latest keys production)", "build:ci:dev:android": "eas build --profile development --platform android --non-interactive", "build:ci:dev:ios": "eas build --profile development --platform ios --non-interactive", "build:ci:preview": "eas build --profile preview --platform ios --local", "build:ci:prod:android": "eas build --profile production --platform android --non-interactive", "build:ci:prod:ios": "eas build --profile production --platform ios --non-interactive", "build:ci:prod:android:submit": "eas build --profile production --platform android --non-interactive --auto-submit", "build:ci:prod:ios:submit": "eas build --profile production --platform ios --non-interactive --auto-submit", "submit:local:prod:ios": "eas submit -p ios --latest", "build:local:dev:ios": "eas build --profile development --platform ios --local --non-interactive ", "build:local:dev:android": "eas build --profile development --platform android --local --non-interactive", "build:local:prod:ios": "eas build --profile production --platform ios --local --non-interactive", "build:local:prod:android": "eas build --profile production --platform android --local --non-interactive", "eas-build-post-install": "node ./scripts/eas-build-post-install.zx.mjs", "deploy:dev:link": "firebase deploy --only hosting:fly-fit-dev-link,functions:optimizedLinking --project fly-fit-dev", "deploy:prod:link": "firebase deploy --only hosting:fly-fit-link,functions:optimizedLinking --project fly-fit", "deploy:local:dev": "firebase deploy --only firestore,storage,functions,hosting:fly-fit-dev-link,hosting:fly-fit-dev-sign-up  --project fly-fit-dev", "deploy:local:dev:only": "firebase deploy --only functions:signUpAppUser,functions:signUpOperation --project fly-fit-dev", "deploy:local:prod": "firebase deploy --only firestore,storage,functions,hosting:fly-fit-link,hosting:fly-fit-sign-up --project fly-fit", "deploy:local:prod:only": "firebase deploy --only functions:sendLoginEmail,functions:sendWelcomeEmail,functions:importUsers --project fly-fit", "deploy:ci:dev:content": "node ./scripts/update-app-config.zx.mjs --env dev && node ./scripts/update-wellness-blogs.zx.mjs --env dev && node ./scripts/update-wellness-quiz.zx.mjs --env dev && node ./scripts/update-wellness-quotes.zx.mjs --env dev", "deploy:ci:prod:content": "node ./scripts/update-app-config.zx.mjs --env prod && node ./scripts/update-wellness-blogs.zx.mjs --env prod && node ./scripts/update-wellness-quiz.zx.mjs --env prod && node ./scripts/update-wellness-quotes.zx.mjs --env prod", "deploy:ci:dev": "firebase deploy --only firestore,storage,functions,hosting:fly-fit-dev-link,hosting:fly-fit-dev-sign-up --project fly-fit-dev --non-interactive --force && npm run deploy:ci:dev:content", "deploy:ci:prod": "firebase deploy --only firestore,storage,functions,hosting:fly-fit-link,hosting:fly-fit-sign-up --project fly-fit --non-interactive --force && npm run deploy:ci:prod:content", "deploy:ota-update:dev": "eas update --channel development --auto --clear-cache --non-interactive", "deploy:ota-update:prod": "eas update --channel production --auto --clear-cache --non-interactive", "build:inspect:prod": "eas build:inspect --platform ios --stage archive --output ./eas-output --force", "submit:production": "eas submit --profile production --platform ios --latest", "submit:android": "eas submit --profile production --platform android --latest", "submit:ios:local": "eas submit -p ios --path build-1695346606491.ipa --profile production", "submit:android:local": "eas submit -p android --path build-1710129468300.aab --profile production", "import": "echo See scripts/import.runbook.md", "backup": "echo See GCP Disaster Recovery page", "export:dev": "node scripts/export-users.zx.mjs --env dev", "export:prod": "node scripts/export-users.zx.mjs --env prod", "postinstall": "patch-package", "prepare": "husky", "setup": "concurrently -m 1 -c auto -i npm:setup:*", "setup:macos": "brew bundle install --file=./Brewfile", "setup:cli": "npm i -g firebase-tools dotenv-vault eas-cli expo-doctor bun @expo/ngrok@^4.1.0 sharp-cli@^2.1.0 cloc", "setup:dotenv": "npx dotenv-vault login --yes && npm run dotenv:pull:dev", "setup:eas": "npx eas login", "setup:firebase": "firebase login && firebase use dev", "setup:gh": "gh auth login && gh auth token > gh_token", "dotenv:pull:dev": "npx dotenv-vault pull development --yes", "dotenv:push:dev": "npx dotenv-vault push development --yes", "dotenv:pull:prod": "npx dotenv-vault pull production --yes", "dotenv:push:prod": "npx dotenv-vault push production --yes", "doctor": "npx expo-doctor --verbose", "update-deps": "npx --yes npm-check-updates -u && npm i", "analyze": "npx react-native-bundle-visualizer --platform ios --expo true", "clean": "node ./scripts/clean.zx.mjs", "release": "semantic-release --no-ci --dry-run false"}, "dependencies": {"@babel/runtime": "^7.26.0", "@expo-google-fonts/epilogue": "^0.2.3", "@expo/html-elements": "^0.11.1", "@expo/metro-runtime": "~4.0.1", "@expo/react-native-action-sheet": "^4.1.0", "@formatjs/intl-datetimeformat": "^6.17.2", "@formatjs/intl-displaynames": "^6.8.9", "@formatjs/intl-getcanonicallocales": "^2.5.4", "@formatjs/intl-locale": "^4.2.9", "@formatjs/intl-numberformat": "^8.15.2", "@formatjs/intl-pluralrules": "^5.4.2", "@formatjs/intl-relativetimeformat": "^11.4.9", "@react-native-firebase/app": "^21.7.0", "@react-native-firebase/auth": "^21.7.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@shopify/flash-list": "1.7.3", "@tanstack/query-sync-storage-persister": "^5.64.1", "@tanstack/react-query": "^5.64.1", "@tanstack/react-query-persist-client": "^5.64.1", "axios": "^1.8.4", "babel-plugin-react-compiler": "beta", "core-js": "^3.42.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "deepmerge": "^4.3.1", "dequal": "^2.0.3", "expo": "~52.0.46", "expo-application": "~6.0.2", "expo-asset": "~11.0.3", "expo-background-fetch": "~13.0.6", "expo-build-properties": "~0.13.3", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.5", "expo-crypto": "~14.0.2", "expo-device": "~7.0.3", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-health-connect": "^0.1.1", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-insights": "~0.8.2", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-network": "~7.0.5", "expo-notifications": "~0.29.14", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-store-review": "~8.0.1", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "firebase": "^11.6.0", "jotai": "^2.12.2", "jotai-tanstack-query": "^0.9.0", "loglevel": "^1.9.2", "luxon": "^3.5.0", "react": "^18.3.1", "react-compiler-runtime": "beta", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-native": "0.76.9", "react-native-animated-rolling-numbers": "^2.0.0", "react-native-circular-progress-indicator": "^4.4.2", "react-native-confetti-cannon": "^1.5.2", "react-native-console-time-polyfill": "^1.2.3", "react-native-context-menu-view": "^1.16.0", "react-native-dotenv": "^3.4.11", "react-native-draggable-flatlist": "^4.0.1", "react-native-draglist": "^3.8.2", "react-native-email-link": "^1.16.1", "react-native-gesture-handler": "~2.20.2", "react-native-health": "^1.19.0", "react-native-health-connect": "^3.3.2", "react-native-image-keyboard": "^2.2.1", "react-native-keyboard-controller": "^1.15.2", "react-native-mmkv": "^2.12.2", "react-native-paper": "^5.13.1", "react-native-paper-dates": "^0.22.28", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-segmented-control-2": "^2.0.1", "react-native-svg": "15.8.0", "react-native-unistyles": "^2.20.0", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-tracked": "^2.0.1", "scheduler": "^0.25.0", "styled-components": "6.1.13", "styled-system": "^5.1.5", "ts-pattern": "^5.7.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-syntax-import-attributes": "^7.26.0", "@eslint-community/eslint-plugin-eslint-comments": "^4.4.1", "@eslint/js": "^9.27.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@stylistic/eslint-plugin": "^4.2.0", "@types/eslint": "^8.56.2", "@types/jest": "^29.5.14", "@types/luxon": "^3.4.2", "@types/node": "^20.14.10", "@types/react": "18.3.12", "@types/react-native-vector-icons": "^6.4.18", "@types/styled-system": "^5.1.23", "@types/timezoned-date": "^3.0.2", "@types/uuid": "^10.0.0", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-optional-require": "^0.3.1", "babel-preset-expo": "^12.0.7", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "dotenv-vault": "^1.26.2", "eslint": "^9.27.0", "eslint-config-expo": "~9.2.0", "eslint-import-resolver-typescript": "~4.3.5", "eslint-plugin-ban": "^2.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import-x": "^4.12.2", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-no-loops": "^0.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-hooks-addons": "^0.5.0", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-sort": "^4.0.0", "eslint-plugin-testing-library": "^7.2.0", "eslint-plugin-unicorn": "^59.0.1", "eslint-plugin-unused-imports": "^4.1.4", "expo-atlas": "^0.4.0", "firebase-admin": "^13.0.2", "firebase-tools": "^13.29.1", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-expo": "~53.0.5", "jiti": "^1.21.0", "minimatch": "^10.0.1", "mockdate": "^3.0.5", "patch-package": "^8.0.0", "semantic-release": "^24.2.1", "timezoned-date": "^3.0.2", "ts-jest": "^29.2.5", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "uuid": "^11.0.5", "zx": "^8.3.0"}, "overrides": {"@expo/config-plugins": "~9.0.17", "react-native": "0.76.9"}, "expo": {"install": {"exclude": ["jest-expo", "eslint-config-expo"]}, "doctor": {"reactNativeDirectoryCheck": {"enabled": false}}, "autolinking": {"ios": {"exclude": ["react-native-health-connect"], "flags": {"inhibit_warnings": true}}, "android": {"exclude": ["react-native-health"]}}}}