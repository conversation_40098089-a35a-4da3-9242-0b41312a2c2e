import {useCallback, useMemo} from 'react';
import {type SignUpAppUserRequestDTO} from '../types';
import {cleanEmail, cleanPhoneNumber, isValidInviteCodeTest} from '../utils';
import {useNewUserForm, useSetHasError} from './atoms';
import {useInviteCodeParamState} from './useInviteCodeParam';
import {useIsValidEmail, useIsValidPhone, useSignUpMutation} from './useSignUpOperations';
import {useIsSignUpSuccessParamState} from './useSignUpSuccessParamState';

/**
 * Custom hook that encapsulates all the form state and logic for the new user form
 */
// eslint-disable-next-line max-lines-per-function -- needed to co-locate form state
export const useNewUserFormState = () => {
  // Get form state from atom
  const {
    email,
    firstName,
    hasError,
    hasInteractedWithEmail,
    hasInteractedWithPhone,
    inviteCodeMetadata,
    isInviteCodeMetadataValid,
    lastName,
    phoneNumber,
  } = useNewUserForm();

  // Get URL parameters
  const [inviteCodeParam] = useInviteCodeParamState();
  const [isSignUpSuccess, setIsSignUpSuccess] = useIsSignUpSuccessParamState();

  // Validate invite code
  const isInviteCodeValidForSubmit =
    !inviteCodeParam ||
    (!!inviteCodeParam && isValidInviteCodeTest(inviteCodeParam) && isInviteCodeMetadataValid);

  // Validate email and phone using the API only after user interaction
  const {data: emailValidation, isPending: isValidatingEmail} = useIsValidEmail(
    hasInteractedWithEmail ? email : undefined,
  );
  const {data: phoneValidation, isPending: isValidatingPhone} = useIsValidPhone(
    hasInteractedWithPhone ? phoneNumber : undefined,
  );

  // Sign up mutation
  const {isPending: isSigningUp, mutate: signUp} = useSignUpMutation();

  // Determine if form is valid for submission
  const isValidForSubmit =
    !!emailValidation?.isValid &&
    !!phoneValidation?.isValid &&
    !!firstName &&
    !!lastName &&
    isInviteCodeValidForSubmit &&
    !isSigningUp;

  // Get validation message for form
  const validationMessage = (() => {
    if (!firstName) return 'Please enter your first name';
    if (!lastName) return 'Please enter your last name';
    if (!emailValidation?.isValid) {
      if (isValidatingEmail) return 'Still validating email...';
      if (email && hasInteractedWithEmail) {
        return emailValidation?.errorMessage || 'Invalid email address';
      }

      return 'Please enter a valid email address';
    }
    if (!phoneValidation?.isValid) {
      if (isValidatingPhone) return 'Still validating phone number...';
      if (phoneNumber && hasInteractedWithPhone) {
        return phoneValidation?.errorMessage || 'Invalid phone number';
      }

      return 'Please enter a valid phone number';
    }
    if (!isInviteCodeValidForSubmit) {
      return 'Please enter a valid invite code (format: F-XXXXXX)';
    }
    if (!isInviteCodeMetadataValid) {
      return 'Please finish selecting your invite code options';
    }

    if (isSigningUp) {
      return 'Signing up...';
    }

    return '';
  })();

  // Submission data
  const submissionData: SignUpAppUserRequestDTO = useMemo(
    () => ({
      email: cleanEmail(email),
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      phoneNumber: cleanPhoneNumber(phoneNumber),
      ...(isInviteCodeValidForSubmit && {
        inviteCodeMetadata,
      }),
    }),
    [email, firstName, inviteCodeMetadata, isInviteCodeValidForSubmit, lastName, phoneNumber],
  );

  // Handle form submission
  const setHasError = useSetHasError();
  const handleSubmit = useCallback(
    async (event: React.FormEvent) => {
      event.preventDefault();
      // console.log({submissionData});

      signUp(submissionData, {
        onSuccess: () => {
          setIsSignUpSuccess(true);
        },
        onError: () => {
          setHasError(true);
          setIsSignUpSuccess(false);
        },
      });
    },
    [signUp, submissionData, setIsSignUpSuccess, setHasError],
  );

  return {
    // Form state
    hasError,
    isSignUpSuccess,
    // Validation state
    emailValidation,
    phoneValidation,
    isValidatingEmail,
    isValidatingPhone,
    isValidForSubmit,
    isInviteCodeValidForSubmit,
    isSigningUp,
    validationMessage,
    // Handlers
    handleSubmit,
  };
};
