import {useMutation, useQuery} from '@tanstack/react-query';
import {query, where} from 'firebase/firestore';
import {useMemo} from 'react';
import type {AxiosResponse} from 'axios';
import {firebaseApi} from '@api';
import {db} from '@backend';
import {CONTENT_CODES, DOMAIN_CONSTANTS, MS_1_MINUTE} from '@constants';
import {
  createUseFirestoreDocumentsByIds,
  useFirestoreDocument,
  useFirestoreDocumentPartialMutation,
  useFirestoreDocumentPartialMutationId,
  useFirestoreQuery,
} from '@hooks';
import {
  type MetadataHealthDataSummary,
  StatusCodes,
  transformAnyTimestampToFirebaseClientTimestampAndRemoveUndefined,
  type UUIDString,
} from '@types';
import {alertConfirm, getEditAppUserOnly, LOGGER} from '@utils';
import {createUseDocumentById} from './firestoreHelpers';

//** CREATE/UPDATE **/
export const useAppUserPartialMutation = (id: UUIDString) =>
  useFirestoreDocumentPartialMutation(
    db.appUsers,
    id,
    transformAnyTimestampToFirebaseClientTimestampAndRemoveUndefined(),
  );

export const useAppUserPartialMutationId = () =>
  useFirestoreDocumentPartialMutationId(
    db.appUsers,
    transformAnyTimestampToFirebaseClientTimestampAndRemoveUndefined(),
  );

//** READ **/

export const useAppUserById = createUseDocumentById(db.appUsers);

export const useAppUserByEmail = (email: string) => {
  const {data, error, isLoading} = useFirestoreQuery({
    q: () => query(db.appUsers, where('email', '==', email)),
    queryKey: ['useAppUserByEmail', email],
    isEnabled: !!email,
    collectionReference: db.appUsers,
  });
  const appUser = data?.[0];
  return {appUser, isLoading, error};
};

export const useGetIsValidNewUserCredentials = (credentials: {
  email?: string | undefined;
  phoneNumber?: string | undefined;
}) =>
  useQuery({
    queryKey: ['useGetIsValidNewUserCredentials', credentials.email, credentials.phoneNumber],
    queryFn: async () => {
      const isPhone = Object.keys(credentials).includes('phoneNumber');
      if (!credentials.email && !credentials.phoneNumber && !isPhone) {
        return {isValid: false, errorMessage: 'Email is required to create an account'};
      }
      if (isPhone && credentials.phoneNumber === undefined) {
        return {
          ...credentials,
          isValid: true,
        };
      }
      if (
        credentials.email?.includes('flybodies.co') &&
        !DOMAIN_CONSTANTS().SPECIAL_USERS.FLY_BODIES_CO_USERS.includes(credentials.email)
      ) {
        return {
          isValid: false,
          errorMessage: 'Please use your own email address. You do not have access to this email.',
        };
      }
      try {
        const response = await firebaseApi.isValidNewUserCredentials(credentials);
        return response.data;
      } catch {
        return {
          isValid: false,
          errorMessage: isPhone
            ? 'Must be a 10-digit phone number that is not yet taken'
            : 'Must be a valid email address that is not yet taken',
        };
      }
    },
    enabled: !!credentials,
    staleTime: MS_1_MINUTE,
  });

export const useBaseAppUsersByIds = createUseFirestoreDocumentsByIds(db.baseAppUsers);

export const useBaseAppUserById = createUseDocumentById(db.baseAppUsers);

export const useEditAppUserOnly = (userId: UUIDString) => {
  const appUser = useAppUserById(userId);
  return useMemo(() => appUser && getEditAppUserOnly(appUser), [appUser]);
};

export const useAppUserMetadataHealthData = (userId: UUIDString) =>
  useFirestoreDocument(db.appUserMetadata(userId), 'healthData') as
  | MetadataHealthDataSummary
  | undefined;

//** DELETE **/

const createUseDeleteWithCallback =
  (
    options: {body: string; header: string; label: string},
    callback: (id: UUIDString) => Promise<AxiosResponse<void, unknown>>,
  ) =>
    () =>
      useMutation({
        mutationFn: (id: UUIDString) =>
          alertConfirm(
            options.header,
            options.body,
            async () => {
              LOGGER.debug(`[Alert ${options.label}] Deleting document started confirmation`);
              const response = await callback(id);
              if (response.status !== StatusCodes.OK_200) {
                throw new Error(`[Alert ${options.label}] Failed to delete ${id}`);
              }
              LOGGER.debug(`[Alert ${options.label}] Deleting document confirmed: ${id}`);
            },
            () => {
              LOGGER.debug(`[Alert ${options.label}] Deleting document cancelled: ${id}`);
            },
          ),
        mutationKey: ['useDeleteWithCallback', options.label],
      });

export const useDeleteAppUserById = createUseDeleteWithCallback(
  {
    body: CONTENT_CODES().EDIT_USER.ALERT_DELETE.BODY,
    header: CONTENT_CODES().EDIT_USER.ALERT_DELETE.HEADER,
    label: 'app user',
  },
  firebaseApi.deleteAppUserById,
);
